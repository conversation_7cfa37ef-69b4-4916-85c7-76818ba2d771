{% load currency_filters %}
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>{{ title }}</title>
    <style>
        @page {
            size: a4 portrait;
            margin: 1cm;
        }
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap');

        body {
            font-family: 'Open Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: {{ template.text_color|default:"#333333" }};
            background-color: {{ template.background_color|default:"#ffffff" }};
        }

        /* Professional report container */
        .report-container {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        {% if is_preview %}
        /* Preview mode styles */
        .preview-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #e11d48;
            color: white;
            text-align: center;
            padding: 10px;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
        }

        .preview-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            padding: 10px;
            text-align: center;
            z-index: 1000;
        }

        .preview-button {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            background-color: #e11d48;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
        }

        .preview-button.secondary {
            background-color: #6c757d;
        }

        body {
            margin-top: 50px;
            margin-bottom: 60px;
        }
        {% endif %}
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid {{ template.accent_color|default:"#e11d48" }};
        }

        .header h1 {
            font-size: 24px;
            margin: 0;
            color: {{ template.header_color|default:"#e11d48" }};
            font-weight: 700;
        }

        .header h2 {
            font-size: 18px;
            margin: 10px 0;
            color: {{ template.accent_color|default:"#e11d48" }};
            font-weight: 600;
        }

        .header p {
            font-size: 14px;
            margin: 5px 0;
            color: {{ template.text_color|default:"#333333" }};
        }
        .summary {
            margin-bottom: 30px;
        }
        .summary-card {
            border: 1px solid {{ template.accent_color|default:"#e11d48" }};
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: rgba(0, 0, 0, 0.02);
            text-align: center;
        }

        .summary-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: {{ template.header_color|default:"#e11d48" }};
        }

        .summary-value {
            font-size: 22px;
            font-weight: bold;
            color: {{ template.accent_color|default:"#e11d48" }};
        }

        /* Dual currency styles for PDF */
        .currency-primary {
            font-weight: bold;
            display: block;
        }
        .currency-secondary {
            font-size: 10px;
            color: #666;
            font-weight: normal;
            display: block;
            margin-top: 2px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            border: 1px solid #e0e0e0;
        }
        th {
            background-color: {{ template.table_header_color|default:"#f8f9fa" }};
            color: {{ template.text_color|default:"#333333" }};
            font-weight: 600;
            text-align: left;
            padding: 10px;
            border-bottom: 2px solid {{ template.accent_color|default:"#e11d48" }};
            font-size: 12px;
        }

        td {
            padding: 10px;
            border-bottom: 1px solid #e0e0e0;
            font-size: 11px;
        }

        tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            color: {{ template.header_color|default:"#e11d48" }};
            padding: 8px 0;
            border-bottom: 1px solid {{ template.accent_color|default:"#e11d48" }};
        }
        .footer {
            text-align: center;
            font-size: 11px;
            color: {{ template.text_color|default:"#333333" }};
            margin-top: 40px;
            border-top: 1px solid #e0e0e0;
            padding-top: 15px;
            {% if not template.show_footer %}display: none;{% endif %}
        }
    </style>
</head>
<body>
    {% if is_preview %}
    <div class="preview-banner">
        Preview Mode - {{ format_type|upper }} Format
    </div>
    {% endif %}

    <div class="report-container">
        <div class="header">
            {% if template.show_logo %}
            <h1>LEGEND FITNESS</h1>
            {% endif %}
            <h2>{{ title }}</h2>
            <p>Period: {{ date_range }}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <div class="summary-title">Total Expenses</div>
                <div class="summary-value">
                    <span class="currency-primary">{{ total_expenses|format_khr }}</span>
                    <span class="currency-secondary">{{ total_expenses|convert_khr_to_usd|format_usd }}</span>
                </div>
            </div>
        </div>

        <!-- Payment Method Breakdown -->
        <div class="payment-method-breakdown">
            <h3>Payment Method Breakdown</h3>
            <table style="margin-bottom: 20px;">
                <thead>
                    <tr>
                        <th>Payment Method</th>
                        <th>Amount (KHR)</th>
                        <th>Amount (USD)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Cash Payments</td>
                        <td class="currency-primary">{{ expense_payment_methods.cash|format_khr }}</td>
                        <td class="currency-secondary">{{ expense_payment_methods.cash|convert_khr_to_usd|format_usd }}</td>
                    </tr>
                    <tr>
                        <td>Bank Transfer</td>
                        <td class="currency-primary">{{ expense_payment_methods.bank|format_khr }}</td>
                        <td class="currency-secondary">{{ expense_payment_methods.bank|convert_khr_to_usd|format_usd }}</td>
                    </tr>
                    <tr style="font-weight: bold; border-top: 2px solid #333;">
                        <td>Total Expenses</td>
                        <td class="currency-primary">{{ total_expenses|format_khr }}</td>
                        <td class="currency-secondary">{{ total_expenses|convert_khr_to_usd|format_usd }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Salary Payments -->
    <div class="section-title">Salary Payments ({{ salary_total|format_dual_currency }})</div>
    {% if salary_payments %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Payroll ID</th>
                <th>Employee</th>
                <th>Position</th>
                <th>Amount (KHR / USD)</th>
            </tr>
        </thead>
        <tbody>
            {% for payment in salary_payments %}
            <tr>
                <td>{{ payment.payment_date|date:"d-M-Y" }}</td>
                <td>{{ payment.payroll_id }}</td>
                <td>{{ payment.employee.name }}</td>
                <td>{{ payment.employee.get_role_display }}</td>
                <td>
                    <span class="currency-primary">{{ payment.final_pay|format_khr }}</span>
                    <span class="currency-secondary">{{ payment.final_pay|convert_khr_to_usd|format_usd }}</span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No salary payments found for this period.</p>
    {% endif %}

    <!-- Bill Payments -->
    <div class="section-title">Bill Payments ({{ bill_total|format_dual_currency }})</div>
    {% if bill_payments %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Bill ID</th>
                <th>Category</th>
                <th>Provider</th>
                <th>Amount (KHR / USD)</th>
                <th>Paid By</th>
            </tr>
        </thead>
        <tbody>
            {% for bill in bill_payments %}
            <tr>
                <td>{{ bill.payment_date|date:"d-M-Y" }}</td>
                <td>{{ bill.bill_id }}</td>
                <td>{{ bill.get_category_display }}</td>
                <td>{{ bill.provider }}</td>
                <td>
                    <span class="currency-primary">{{ bill.amount_khr|format_khr }}</span>
                    <span class="currency-secondary">{{ bill.amount_khr|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ bill.paid_by.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No bill payments found for this period.</p>
    {% endif %}

    <!-- Product Purchases -->
    <div class="section-title">Product Purchases ({{ purchase_total|format_dual_currency }})</div>
    {% if product_purchases %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Transaction ID</th>
                <th>Supplier</th>
                <th>Amount (KHR / USD)</th>
                <th>Created By</th>
            </tr>
        </thead>
        <tbody>
            {% for purchase in product_purchases %}
            <tr>
                <td>{{ purchase.date|date:"d-M-Y" }}</td>
                <td>{{ purchase.trxId }}</td>
                <td>{{ purchase.supplier.name|default:"Unknown" }}</td>
                <td>
                    <span class="currency-primary">{{ purchase.total_amount|format_khr }}</span>
                    <span class="currency-secondary">{{ purchase.total_amount|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ purchase.created_by.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No product purchases found for this period.</p>
    {% endif %}

    <!-- Withdrawals -->
    <div class="section-title">Withdrawals ({{ withdrawal_total|format_dual_currency }})</div>
    {% if withdrawals %}
    <table>
        <thead><tr>
                <th>Date</th>
                <th>Transaction ID</th>
                <th>Description</th>
                <th>Amount (KHR / USD)</th>
                <th>Created By</th>
            </tr>
        </thead>
        <tbody>
            {% for withdrawal in withdrawals %}
            <tr>
                <td>{{ withdrawal.transaction_date|date:"d-M-Y" }}</td>
                <td>{{ withdrawal.transaction_id }}</td>
                <td>{{ withdrawal.description|default:"Cash Withdrawal" }}</td>
                <td>
                    <span class="currency-primary">{{ withdrawal.amount_khr|format_khr }}</span>
                    <span class="currency-secondary">{{ withdrawal.amount_khr|convert_khr_to_usd|format_usd }}</span>
                </td>
                <td>{{ withdrawal.staff.username }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No withdrawals found for this period.</p>
    {% endif %}

    {% if template.show_footer %}
    <div class="footer">
        <p>Generated on {{ now|date:"d-M-Y H:i" }} | {{ template.footer_text|default:"Legend Fitness Club" }}</p>
    </div>
    {% endif %}

    {% if is_preview %}
    <div class="preview-actions">
        {% if format_type == 'pdf' %}
        <a href="{% url 'financialreport:export_pdf' 'expense' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}" class="preview-button">
            <i class="fa-solid fa-download"></i> Download PDF
        </a>
        {% elif format_type == 'csv' %}
        <a href="{% url 'financialreport:export_csv' 'expense' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}" class="preview-button">
            <i class="fa-solid fa-download"></i> Download CSV
        </a>
        {% elif format_type == 'print' %}
        <button onclick="window.print()" class="preview-button"><i class="fa-solid fa-print"></i> Print Report</button>
        {% endif %}
        <a href="javascript:window.close()" class="preview-button secondary">
            <i class="fa-solid fa-times"></i> Close Preview
        </a>
    </div>
    {% endif %}
</body>
</html>
